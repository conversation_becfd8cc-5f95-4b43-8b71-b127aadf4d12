package com.weinuo.quickcommands.ui.components.skyblue

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.SimpleAppInfo
import com.weinuo.quickcommands.data.SmartReminderType
import com.weinuo.quickcommands.navigation.Screen
import com.weinuo.quickcommands.service.QuickCommandsService
import com.weinuo.quickcommands.storage.SmartReminderConfigAdapter
import com.weinuo.quickcommands.storage.UIStateStorageManager
import com.weinuo.quickcommands.ui.activities.AppSelectionActivity
import com.weinuo.quickcommands.ui.configuration.ConfigurationCardItem
import kotlinx.coroutines.launch

/**
 * 音乐应用提醒配置Provider
 *
 * 提供音乐应用提醒功能的配置界面和逻辑。
 * 遵循Provider架构模式，便于扩展和维护。
 *
 * 配置项包括：
 * - 选择音乐应用（必需配置）
 * - 延迟提醒时间（1-60秒）
 * - 提醒间隔配置（30-600秒）
 * - 自动消失设置（1-60秒）
 * - 按钮显示配置（位置、尺寸、边距）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
object SkyBlueMusicAppReminderConfigProvider {

    /**
     * 获取音乐应用提醒配置项
     */
    fun getConfigurationItem(context: Context): ConfigurationCardItem<SmartReminderType> {
        return ConfigurationCardItem(
            id = "music_app_reminder",
            title = context.getString(R.string.music_app_reminder_title),
            description = context.getString(R.string.music_app_reminder_description),
            operationType = SmartReminderType.MUSIC_APP_REMINDER,
            permissionRequired = true, // 音乐应用提醒需要悬浮窗权限
            content = { reminderType, onComplete ->
                MusicAppReminderConfigContent(reminderType, onComplete)
            },
            editableContent = { reminderType, initialConfig, onComplete ->
                MusicAppReminderConfigContent(reminderType, onComplete, initialConfig)
            }
        )
    }

    /**
     * 获取配置内容组件（支持外部保存请求）
     *
     * 提供音乐应用提醒的配置内容组件，用于在智慧提醒详细配置界面中显示。
     * 支持外部保存请求机制，用于右上角保存按钮。
     *
     * @param reminderType 智慧提醒类型
     * @param onComplete 配置完成回调
     * @param onSaveRequested 外部保存请求回调，用于暴露保存函数给外部调用
     * @param navController 导航控制器，用于应用选择
     */
    @Composable
    fun getConfigContent(
        reminderType: SmartReminderType,
        onComplete: (Any) -> Unit,
        onSaveRequested: ((suspend () -> Unit) -> Unit)? = null,
        navController: NavController? = null
    ) {
        MusicAppReminderConfigContent(
            reminderType = reminderType,
            onComplete = onComplete,
            onSaveRequested = onSaveRequested,
            navController = navController
        )
    }
}

/**
 * 音乐应用提醒配置内容组件
 *
 * 提供完整的音乐应用提醒配置选项：
 * - 选择多个音乐应用（支持多选）
 * - 延迟提醒时间设置（1-60秒）
 * - 提醒间隔配置（30-600秒）
 * - 自动消失设置（1-60秒）
 * - 多按钮配置（位置、尺寸、边距）
 *
 * @param reminderType 智慧提醒类型
 * @param onComplete 配置完成回调
 * @param initialConfig 初始配置（编辑模式使用）
 * @param onSaveRequested 外部保存请求回调，用于暴露保存函数给外部调用
 * @param navController 导航控制器，用于应用选择
 */
@Composable
private fun MusicAppReminderConfigContent(
    reminderType: SmartReminderType,
    onComplete: (Any) -> Unit,
    initialConfig: Any? = null,
    onSaveRequested: ((suspend () -> Unit) -> Unit)? = null,
    navController: NavController? = null
) {
    val context = LocalContext.current
    val configAdapter = remember { SmartReminderConfigAdapter(context) }
    val coroutineScope = rememberCoroutineScope()

    // 配置状态
    var selectedMusicApps by remember { mutableStateOf(listOf<SmartReminderConfigAdapter.SelectedMusicApp>()) }
    var buttonConfigs by remember { mutableStateOf(listOf<SmartReminderConfigAdapter.MusicAppButtonConfig>()) }
    var delayTime by remember { mutableStateOf("2") }
    var cooldownTime by remember { mutableStateOf("60") }
    var autoDismissEnabled by remember { mutableStateOf(true) }
    var autoDismissSeconds by remember { mutableStateOf("10") }

    // 应用选择ActivityResultLauncher
    val appSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val selectedPackageNames = result.data?.getStringArrayListExtra("selected_apps") ?: emptyList()

            // 获取应用信息
            val packageManager = context.packageManager
            val newSelectedApps = selectedPackageNames.mapNotNull { packageName ->
                try {
                    val appInfo = packageManager.getApplicationInfo(packageName, 0)
                    val appName = packageManager.getApplicationLabel(appInfo).toString()
                    SmartReminderConfigAdapter.SelectedMusicApp(
                        packageName = packageName,
                        appName = appName,
                        isEnabled = true
                    )
                } catch (e: Exception) {
                    null
                }
            }
            selectedMusicApps = newSelectedApps

            // 为新选择的应用生成按钮配置
            buttonConfigs = newSelectedApps.mapIndexed { index, app ->
                val (position, marginX, marginY) = calculateButtonPosition(index)
                SmartReminderConfigAdapter.MusicAppButtonConfig(
                    appPackageName = app.packageName,
                    buttonPosition = position,
                    buttonSize = 56,
                    buttonMarginX = marginX,
                    buttonMarginY = marginY,
                    isEnabled = true
                )
            }
        }
    }

    // 处理应用选择结果
    LaunchedEffect(navController) {
        navController?.currentBackStackEntry
            ?.savedStateHandle
            ?.getLiveData<List<String>>("smart_reminder_${reminderType.id}_apps")
            ?.observeForever { selectedPackageNames ->
                if (selectedPackageNames != null) {
                    // 获取应用信息
                    val packageManager = context.packageManager
                    val newSelectedApps = selectedPackageNames.mapNotNull { packageName ->
                        try {
                            val appInfo = packageManager.getApplicationInfo(packageName, 0)
                            val appName = packageManager.getApplicationLabel(appInfo).toString()
                            SmartReminderConfigAdapter.SelectedMusicApp(
                                packageName = packageName,
                                appName = appName,
                                isEnabled = true
                            )
                        } catch (e: Exception) {
                            null
                        }
                    }
                    selectedMusicApps = newSelectedApps

                    // 为新选择的应用生成按钮配置
                    buttonConfigs = newSelectedApps.mapIndexed { index, app ->
                        buttonConfigs.find { it.appPackageName == app.packageName }
                            ?: run {
                                val (position, marginX, marginY) = calculateButtonPosition(index)
                                SmartReminderConfigAdapter.MusicAppButtonConfig(
                                    appPackageName = app.packageName,
                                    buttonPosition = position,
                                    buttonSize = 56,
                                    buttonMarginX = marginX,
                                    buttonMarginY = marginY,
                                    isEnabled = true
                                )
                            }
                    }

                    // 清除导航结果
                    navController.currentBackStackEntry?.savedStateHandle?.remove<List<String>>("smart_reminder_${reminderType.id}_apps")
                }
            }
    }




    // 加载初始配置
    LaunchedEffect(initialConfig) {
        if (initialConfig is SmartReminderConfigAdapter.MusicAppReminderConfig) {
            selectedMusicApps = initialConfig.selectedMusicApps
            buttonConfigs = initialConfig.buttonConfigs
            delayTime = initialConfig.delayTime.toString()
            cooldownTime = initialConfig.cooldownTime.toString()
            autoDismissEnabled = initialConfig.autoDismissEnabled
            autoDismissSeconds = initialConfig.autoDismissSeconds.toString()
        } else {
            // 尝试加载现有配置
            try {
                val existingConfig = configAdapter.loadMusicAppReminderConfig(reminderType.id)
                selectedMusicApps = existingConfig.selectedMusicApps
                buttonConfigs = existingConfig.buttonConfigs
                delayTime = existingConfig.delayTime.toString()
                cooldownTime = existingConfig.cooldownTime.toString()
                autoDismissEnabled = existingConfig.autoDismissEnabled
                autoDismissSeconds = existingConfig.autoDismissSeconds.toString()
            } catch (e: Exception) {
                // 使用默认值
            }
        }
    }

    // 保存配置函数
    val saveConfig: suspend () -> Unit = {
        try {
            val config = SmartReminderConfigAdapter.MusicAppReminderConfig(
                selectedMusicApps = selectedMusicApps,
                buttonConfigs = buttonConfigs,
                delayTime = delayTime.toIntOrNull()?.coerceIn(1, 60) ?: 2,
                cooldownTime = cooldownTime.toIntOrNull()?.coerceIn(30, 600) ?: 60,
                autoDismissEnabled = autoDismissEnabled,
                autoDismissSeconds = autoDismissSeconds.toIntOrNull()?.coerceIn(1, 60) ?: 10
            )

            // 保存配置
            configAdapter.saveMusicAppReminderConfig(reminderType.id, config)

            // 通知服务重新加载配置
            QuickCommandsService.recheckSmartReminderMonitoring(context)

            // 注意：这里不调用onComplete，避免自动跳转
            // onComplete(config)

        } catch (e: Exception) {
            // 处理保存错误
        }
    }

    // 暴露保存函数给外部调用
    LaunchedEffect(onSaveRequested) {
        onSaveRequested?.invoke(saveConfig)
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        // 应用选择配置 - 与其他配置项保持一致的样式
        ConfigSection(
            title = if (selectedMusicApps.isEmpty()) "选择音乐应用" else "更换音乐应用",
            description = if (selectedMusicApps.isEmpty()) {
                "选择要监控的音乐应用，当这些应用启动时将显示提醒按钮"
            } else {
                "已选择 ${selectedMusicApps.size} 个应用，点击按钮可重新选择"
            }
        ) {
            Button(
                onClick = {
                    // 启动应用选择界面
                    val currentSelectedApps = selectedMusicApps.map { it.packageName }
                    val intent = Intent(context, AppSelectionActivity::class.java).apply {
                        putExtra("selection_mode", "MULTI")
                        putStringArrayListExtra("initial_selected_apps", ArrayList(currentSelectedApps))
                        putExtra("result_key", "smart_reminder_${reminderType.id}_apps")
                    }
                    appSelectionLauncher.launch(intent)
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (selectedMusicApps.isEmpty()) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.surfaceVariant
                    },
                    contentColor = if (selectedMusicApps.isEmpty()) {
                        MaterialTheme.colorScheme.onPrimary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            ) {
                Text(
                    text = if (selectedMusicApps.isEmpty()) "选择应用" else "重新选择应用"
                )
            }

            // 显示已选择的应用列表
            if (selectedMusicApps.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                    Text(
                        text = "已选择的应用：",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    selectedMusicApps.forEach { app ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "• ${app.appName}",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurface,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
            }
        }

        // 按钮位置配置（仅在有选中应用时显示）
        if (selectedMusicApps.isNotEmpty()) {
            ConfigSection(
                title = "按钮位置",
                description = "设置提醒按钮在屏幕上的显示位置"
            ) {
            val positionOptions = listOf(
                "bottom_left" to "左下角",
                "bottom_right" to "右下角",
                "top_left" to "左上角",
                "top_right" to "右上角"
            )

            // 为每个应用显示按钮位置配置
            selectedMusicApps.forEachIndexed { index, app ->
                if (app.isEnabled) {
                    val appButtonConfig = buttonConfigs.find { it.appPackageName == app.packageName }
                        ?: run {
                            val (position, marginX, marginY) = calculateButtonPosition(index)
                            SmartReminderConfigAdapter.MusicAppButtonConfig(
                                appPackageName = app.packageName,
                                buttonPosition = position,
                                buttonSize = 56,
                                buttonMarginX = marginX,
                                buttonMarginY = marginY,
                                isEnabled = true
                            ).also { newConfig ->
                                buttonConfigs = buttonConfigs + newConfig
                            }
                        }

                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            Text(
                                text = app.appName,
                                style = MaterialTheme.typography.titleSmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )

                            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                                positionOptions.forEach { (value, label) ->
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        RadioButton(
                                            selected = appButtonConfig.buttonPosition == value,
                                            onClick = {
                                                buttonConfigs = buttonConfigs.map { config ->
                                                    if (config.appPackageName == app.packageName) {
                                                        config.copy(buttonPosition = value)
                                                    } else config
                                                }
                                            }
                                        )
                                        Text(
                                            text = label,
                                            modifier = Modifier.padding(start = 8.dp),
                                            style = MaterialTheme.typography.bodyMedium
                                        )
                                    }
                                }
                            }
                        }
                    }

                    if (index < selectedMusicApps.filter { it.isEnabled }.size - 1) {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
        }
        }

        // 按钮样式配置（仅在有选中应用时显示）
        if (selectedMusicApps.isNotEmpty()) {
        ConfigSection(
            title = "按钮样式",
            description = "设置提醒按钮的尺寸和边距"
        ) {
            selectedMusicApps.forEachIndexed { index, app ->
                if (app.isEnabled) {
                    val appButtonConfig = buttonConfigs.find { it.appPackageName == app.packageName }
                        ?: run {
                            val (position, marginX, marginY) = calculateButtonPosition(index)
                            SmartReminderConfigAdapter.MusicAppButtonConfig(
                                appPackageName = app.packageName,
                                buttonPosition = position,
                                buttonSize = 56,
                                buttonMarginX = marginX,
                                buttonMarginY = marginY,
                                isEnabled = true
                            ).also { newConfig ->
                                buttonConfigs = buttonConfigs + newConfig
                            }
                        }

                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "${app.appName} 按钮样式",
                                style = MaterialTheme.typography.titleSmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )

                            // 按钮尺寸
                            OutlinedTextField(
                                value = appButtonConfig.buttonSize.toString(),
                                onValueChange = { newValue ->
                                    newValue.toIntOrNull()?.let { size ->
                                        buttonConfigs = buttonConfigs.map { config ->
                                            if (config.appPackageName == app.packageName) {
                                                config.copy(buttonSize = size.coerceIn(40, 80))
                                            } else config
                                        }
                                    }
                                },
                                label = { Text("按钮尺寸（dp）") },
                                placeholder = { Text("56") },
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                singleLine = true,
                                modifier = Modifier.fillMaxWidth()
                            )

                            // 水平边距
                            OutlinedTextField(
                                value = appButtonConfig.buttonMarginX.toString(),
                                onValueChange = { newValue ->
                                    newValue.toIntOrNull()?.let { margin ->
                                        buttonConfigs = buttonConfigs.map { config ->
                                            if (config.appPackageName == app.packageName) {
                                                config.copy(buttonMarginX = margin.coerceIn(8, 100))
                                            } else config
                                        }
                                    }
                                },
                                label = { Text("水平边距（dp）") },
                                placeholder = { Text("24") },
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                singleLine = true,
                                modifier = Modifier.fillMaxWidth()
                            )

                            // 垂直边距
                            OutlinedTextField(
                                value = appButtonConfig.buttonMarginY.toString(),
                                onValueChange = { newValue ->
                                    newValue.toIntOrNull()?.let { margin ->
                                        buttonConfigs = buttonConfigs.map { config ->
                                            if (config.appPackageName == app.packageName) {
                                                config.copy(buttonMarginY = margin.coerceIn(8, 200))
                                            } else config
                                        }
                                    }
                                },
                                label = { Text("垂直边距（dp）") },
                                placeholder = { Text("24") },
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                singleLine = true,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }

                    if (index < selectedMusicApps.filter { it.isEnabled }.size - 1) {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
        }
        }

        // 延迟时间设置
        ConfigSection(
            title = "延迟提醒时间",
            description = "检测到耳机连接后延迟多久提醒，范围：1-60秒"
        ) {
            OutlinedTextField(
                value = delayTime,
                onValueChange = { delayTime = it },
                label = { Text("延迟时间（秒）") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 冷却时间设置
        ConfigSection(
            title = "提醒间隔",
            description = "两次提醒之间的最小间隔，范围：30-600秒"
        ) {
            OutlinedTextField(
                value = cooldownTime,
                onValueChange = { cooldownTime = it },
                label = { Text("间隔时间（秒）") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 自动消失设置
        ConfigSection(
            title = "自动消失",
            description = "设置提醒按钮是否自动消失，避免长时间停留在屏幕上"
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                // 自动消失开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "启用自动消失",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "提醒按钮将在指定时间后自动关闭",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    Switch(
                        checked = autoDismissEnabled,
                        onCheckedChange = { autoDismissEnabled = it }
                    )
                }

                // 自动消失时间设置
                if (autoDismissEnabled) {
                    OutlinedTextField(
                        value = autoDismissSeconds,
                        onValueChange = { autoDismissSeconds = it },
                        label = { Text("自动消失时间（秒）") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }




    }
}



/**
 * 配置区域组件
 * 复用屏幕旋转提醒配置界面的ConfigSection组件
 */
@Composable
private fun ConfigSection(
    title: String,
    description: String,
    content: @Composable () -> Unit
) {
    Card(
        shape = androidx.compose.foundation.shape.RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            content()
        }
    }
}

/**
 * 智能计算按钮位置，避免重叠
 * 支持无限数量的应用，通过增加边距和调整位置来避免重叠
 *
 * @param index 应用索引（从0开始）
 * @return Triple(位置, 水平边距, 垂直边距)
 */
private fun calculateButtonPosition(index: Int): Triple<String, Int, Int> {
    // 基础位置循环：右下角 -> 左下角 -> 右上角 -> 左上角
    val basePositions = listOf("bottom_right", "bottom_left", "top_right", "top_left")
    val position = basePositions[index % 4]

    // 计算层级（每4个应用为一层）
    val layer = index / 4

    // 基础边距
    val baseMarginX = 24
    val baseMarginY = 24

    // 根据层级和位置计算边距，确保不重叠且不超出屏幕
    val (marginX, marginY) = when (position) {
        "bottom_right" -> {
            // 右下角：向左和向上偏移
            val x = baseMarginX + (layer * 80).coerceAtMost(200) // 最大不超过200dp
            val y = baseMarginY + (layer * 80).coerceAtMost(300) // 最大不超过300dp
            Pair(x, y)
        }
        "bottom_left" -> {
            // 左下角：向右和向上偏移
            val x = baseMarginX + (layer * 80).coerceAtMost(200)
            val y = baseMarginY + (layer * 80).coerceAtMost(300)
            Pair(x, y)
        }
        "top_right" -> {
            // 右上角：向左和向下偏移
            val x = baseMarginX + (layer * 80).coerceAtMost(200)
            val y = baseMarginY + (layer * 80).coerceAtMost(300)
            Pair(x, y)
        }
        "top_left" -> {
            // 左上角：向右和向下偏移
            val x = baseMarginX + (layer * 80).coerceAtMost(200)
            val y = baseMarginY + (layer * 80).coerceAtMost(300)
            Pair(x, y)
        }
        else -> Pair(baseMarginX, baseMarginY)
    }

    return Triple(position, marginX, marginY)
}