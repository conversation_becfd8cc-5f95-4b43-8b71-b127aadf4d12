package com.weinuo.quickcommands.ui.components.oceanblue

import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.data.SmartReminderType
import com.weinuo.quickcommands.storage.SmartReminderConfigAdapter
import com.weinuo.quickcommands.ui.configuration.ConfigurationCardItem
import kotlinx.coroutines.launch

/**
 * 分享网址提醒配置提供器
 *
 * 提供分享网址提醒功能的配置界面和逻辑。
 * 支持自定义URL模式、检测延迟、冷却时间等配置。
 *
 * 功能特点：
 * - 自定义URL模式配置
 * - 检测延迟和冷却时间设置
 * - 悬浮窗显示配置
 * - 与其他提醒功能保持一致的配置界面
 *
 * 设计原则：
 * - 遵循现有配置界面的设计模式
 * - 提供直观的配置选项
 * - 支持实时预览和保存
 * - 高可扩展性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
object OceanBlueShareUrlReminderConfigProvider {

    /**
     * 获取分享网址提醒配置项
     */
    fun getConfigurationItem(context: Context): ConfigurationCardItem<SmartReminderType> {
        return ConfigurationCardItem(
            id = "share_url_reminder",
            title = context.getString(R.string.share_url_reminder_title),
            description = context.getString(R.string.share_url_reminder_description),
            operationType = SmartReminderType.SHARE_URL_REMINDER,
            permissionRequired = true, // 分享网址提醒需要悬浮窗权限
            content = { reminderType, onComplete ->
                ShareUrlReminderConfigContent(reminderType, onComplete)
            },
            editableContent = { reminderType, initialConfig, onComplete ->
                ShareUrlReminderConfigContent(reminderType, onComplete, initialConfig)
            }
        )
    }

    /**
     * 获取配置内容组件（支持外部保存请求）
     *
     * 提供分享网址提醒的配置内容组件，用于在智慧提醒详细配置界面中显示。
     * 支持外部保存请求机制，用于右上角保存按钮。
     *
     * @param reminderType 智慧提醒类型
     * @param onComplete 配置完成回调
     * @param onSaveRequested 外部保存请求回调，用于暴露保存函数给外部调用
     */
    @Composable
    fun getConfigContent(
        reminderType: SmartReminderType,
        onComplete: (Any) -> Unit,
        onSaveRequested: ((suspend () -> Unit) -> Unit)? = null
    ) {
        ShareUrlReminderConfigContent(
            reminderType = reminderType,
            onComplete = onComplete,
            onSaveRequested = onSaveRequested
        )
    }
}

/**
 * 分享网址提醒配置内容组件
 *
 * 提供完整的分享网址提醒配置选项：
 * - 自定义URL模式设置
 * - 检测延迟配置（0.1-5秒）
 * - 提醒间隔配置（10-300秒）
 * - 自动消失设置（1-60秒）
 * - 按钮显示配置（位置、尺寸、边距）
 *
 * @param reminderType 智慧提醒类型
 * @param onComplete 配置完成回调
 * @param initialConfig 初始配置（编辑模式使用）
 * @param onSaveRequested 外部保存请求回调，用于暴露保存函数给外部调用
 */
@Composable
private fun ShareUrlReminderConfigContent(
    reminderType: SmartReminderType,
    onComplete: (Any) -> Unit,
    initialConfig: Any? = null,
    onSaveRequested: ((suspend () -> Unit) -> Unit)? = null
) {
    val context = LocalContext.current
    val configAdapter = remember { SmartReminderConfigAdapter(context) }
    val coroutineScope = rememberCoroutineScope()

    // 加载初始配置
    var config by remember {
        mutableStateOf(
            initialConfig as? SmartReminderConfigAdapter.ShareUrlReminderConfig
                ?: configAdapter.loadShareUrlReminderConfig(reminderType.id)
        )
    }

    // 配置状态 - 使用String类型以匹配OverlaySettingsContent的参数要求
    var customUrlPatterns by remember { mutableStateOf(config.customUrlPatterns.toMutableList()) }
    var detectionDelay by remember { mutableStateOf(config.detectionDelay.toString()) }
    var cooldownTime by remember { mutableStateOf(config.cooldownTime.toString()) }
    var autoDismissEnabled by remember { mutableStateOf(config.autoDismissEnabled) }
    var autoDismissSeconds by remember { mutableStateOf(config.autoDismissSeconds.toString()) }
    var buttonPosition by remember { mutableStateOf(config.buttonPosition) }
    var buttonSize by remember { mutableStateOf(config.buttonSize.toString()) }
    var buttonMarginX by remember { mutableStateOf(config.buttonMarginX.toString()) }
    var buttonMarginY by remember { mutableStateOf(config.buttonMarginY.toString()) }

    // 保存配置的函数
    val saveConfig: suspend () -> Unit = {
        val newConfig = SmartReminderConfigAdapter.ShareUrlReminderConfig(
            customUrlPatterns = customUrlPatterns.toList(),
            detectionDelay = detectionDelay.toIntOrNull() ?: 500,
            cooldownTime = cooldownTime.toIntOrNull() ?: 30,
            autoDismissEnabled = autoDismissEnabled,
            autoDismissSeconds = autoDismissSeconds.toIntOrNull() ?: 10,
            buttonPosition = buttonPosition,
            buttonSize = buttonSize.toIntOrNull() ?: 56,
            buttonMarginX = buttonMarginX.toIntOrNull() ?: 24,
            buttonMarginY = buttonMarginY.toIntOrNull() ?: 24
        )

        configAdapter.saveShareUrlReminderConfig(reminderType.id, newConfig)
        onComplete(newConfig)
    }

    // 暴露保存函数给外部
    LaunchedEffect(Unit) {
        onSaveRequested?.invoke(saveConfig)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 功能说明
        Card(
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Share,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Text(
                        text = "功能说明",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
                Text(
                    text = "当您复制网址链接到剪贴板时，会自动显示分享提醒悬浮窗，点击即可快速分享网址到其他应用。",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "• 自动排除应用专用链接，避免与应用链接提醒冲突\n• 支持自定义URL模式扩展检测范围\n• 可配置提醒间隔和悬浮窗样式",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                )
            }
        }
        // 基本设置
        ConfigSection(
            title = "基本设置",
            description = "配置分享网址提醒的基本参数"
        ) {
            DetectionSettingsContent(
                detectionDelay = detectionDelay,
                onDetectionDelayChange = { detectionDelay = it },
                cooldownTime = cooldownTime,
                onCooldownTimeChange = { cooldownTime = it }
            )
        }

        // 悬浮窗设置
        ConfigSection(
            title = "悬浮窗设置",
            description = "设置提醒按钮的显示样式和行为"
        ) {
            OverlaySettingsContent(
                autoDismissEnabled = autoDismissEnabled,
                onAutoDismissEnabledChange = { autoDismissEnabled = it },
                autoDismissSeconds = autoDismissSeconds,
                onAutoDismissSecondsChange = { autoDismissSeconds = it },
                buttonPosition = buttonPosition,
                onButtonPositionChange = { buttonPosition = it },
                buttonSize = buttonSize,
                onButtonSizeChange = { buttonSize = it },
                buttonMarginX = buttonMarginX,
                onButtonMarginXChange = { buttonMarginX = it },
                buttonMarginY = buttonMarginY,
                onButtonMarginYChange = { buttonMarginY = it }
            )
        }

        // 高级设置
        ConfigSection(
            title = "高级设置",
            description = "自定义URL模式和排除规则"
        ) {
            AdvancedSettingsContent(
                customUrlPatterns = customUrlPatterns,
                onCustomUrlPatternsChange = { customUrlPatterns = it }
            )
        }
    }
}

/**
 * 检测设置内容组件
 */
@Composable
private fun DetectionSettingsContent(
    detectionDelay: String,
    onDetectionDelayChange: (String) -> Unit,
    cooldownTime: String,
    onCooldownTimeChange: (String) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 检测延迟设置
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "检测延迟",
                style = MaterialTheme.typography.titleSmall
            )
            OutlinedTextField(
                value = detectionDelay,
                onValueChange = onDetectionDelayChange,
                label = { Text("延迟时间（毫秒）") },
                placeholder = { Text("500") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            Text(
                text = "剪贴板变化后延迟检测的时间，避免频繁触发（建议100-5000毫秒）",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 冷却时间设置
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "提醒间隔",
                style = MaterialTheme.typography.titleSmall
            )
            OutlinedTextField(
                value = cooldownTime,
                onValueChange = onCooldownTimeChange,
                label = { Text("间隔时间（秒）") },
                placeholder = { Text("30") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            Text(
                text = "两次提醒之间的最小间隔时间（建议10-300秒）",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 悬浮窗设置内容组件
 */
@Composable
private fun OverlaySettingsContent(
    autoDismissEnabled: Boolean,
    onAutoDismissEnabledChange: (Boolean) -> Unit,
    autoDismissSeconds: String,
    onAutoDismissSecondsChange: (String) -> Unit,
    buttonPosition: String,
    onButtonPositionChange: (String) -> Unit,
    buttonSize: String,
    onButtonSizeChange: (String) -> Unit,
    buttonMarginX: String,
    onButtonMarginXChange: (String) -> Unit,
    buttonMarginY: String,
    onButtonMarginYChange: (String) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 自动消失设置
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "启用自动消失",
                    style = MaterialTheme.typography.titleSmall
                )
                Text(
                    text = "提醒按钮在指定时间后自动消失",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            Switch(
                checked = autoDismissEnabled,
                onCheckedChange = onAutoDismissEnabledChange
            )
        }

        if (autoDismissEnabled) {
            OutlinedTextField(
                value = autoDismissSeconds,
                onValueChange = onAutoDismissSecondsChange,
                label = { Text("自动消失时间（秒）") },
                placeholder = { Text("10") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
        }

        // 按钮位置设置
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "按钮位置",
                style = MaterialTheme.typography.titleSmall
            )

            val positions = listOf(
                "bottom_left" to "左下角",
                "bottom_right" to "右下角",
                "top_left" to "左上角",
                "top_right" to "右上角"
            )

            positions.forEach { (value, label) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onButtonPositionChange(value) },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = buttonPosition == value,
                        onClick = { onButtonPositionChange(value) }
                    )
                    Text(
                        text = label,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }

        // 按钮尺寸设置
        OutlinedTextField(
            value = buttonSize,
            onValueChange = onButtonSizeChange,
            label = { Text("按钮尺寸（dp）") },
            placeholder = { Text("56") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        // 边距设置
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = buttonMarginX,
                onValueChange = onButtonMarginXChange,
                label = { Text("水平边距（dp）") },
                placeholder = { Text("24") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
            OutlinedTextField(
                value = buttonMarginY,
                onValueChange = onButtonMarginYChange,
                label = { Text("垂直边距（dp）") },
                placeholder = { Text("24") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
        }
    }
}

/**
 * 高级设置内容组件
 */
@Composable
private fun AdvancedSettingsContent(
    customUrlPatterns: MutableList<String>,
    onCustomUrlPatternsChange: (MutableList<String>) -> Unit
) {
    var newPatternText by remember { mutableStateOf("") }
    var showAddDialog by remember { mutableStateOf(false) }
    var testUrlText by remember { mutableStateOf("") }
    var testResult by remember { mutableStateOf<String?>(null) }

    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 自定义URL模式管理
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "自定义URL模式",
                    style = MaterialTheme.typography.titleSmall
                )
                IconButton(
                    onClick = { showAddDialog = true }
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "添加URL模式"
                    )
                }
            }

            Text(
                text = "添加自定义的URL模式来扩展检测范围。支持正则表达式。留空表示检测所有网址。",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            if (customUrlPatterns.isEmpty()) {
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                    )
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "当前未设置自定义模式，将检测所有网址",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            textAlign = androidx.compose.ui.text.style.TextAlign.Center
                        )
                    }
                }
            } else {
                customUrlPatterns.forEachIndexed { index, pattern ->
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(12.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "模式 ${index + 1}",
                                    style = MaterialTheme.typography.labelMedium,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                Text(
                                    text = pattern,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                                )
                            }
                            IconButton(
                                onClick = {
                                    val newList = customUrlPatterns.toMutableList()
                                    newList.removeAt(index)
                                    onCustomUrlPatternsChange(newList)
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Delete,
                                    contentDescription = "删除模式",
                                    tint = MaterialTheme.colorScheme.error
                                )
                            }
                        }
                    }
                }
            }
        }

        // 排除域名列表
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "排除的域名",
                style = MaterialTheme.typography.titleSmall
            )
            Text(
                text = "以下域名的链接将被自动排除，避免与应用链接提醒冲突：",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            val excludedDomains = com.weinuo.quickcommands.smartreminder.ShareUrlDetector.getExcludedDomains()
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                contentPadding = PaddingValues(vertical = 4.dp)
            ) {
                items(excludedDomains.toList()) { domain ->
                    AssistChip(
                        onClick = { },
                        label = {
                            Text(
                                text = domain,
                                style = MaterialTheme.typography.bodySmall
                            )
                        },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Block,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    )
                }
            }
        }

        // URL测试功能
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "URL检测测试",
                style = MaterialTheme.typography.titleSmall
            )
            Text(
                text = "输入URL来测试是否会被检测和分享：",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            OutlinedTextField(
                value = testUrlText,
                onValueChange = { testUrlText = it },
                label = { Text("测试URL") },
                placeholder = { Text("https://example.com") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                trailingIcon = {
                    IconButton(
                        onClick = {
                            try {
                                val result = com.weinuo.quickcommands.smartreminder.ShareUrlDetector.detectShareableUrl(
                                    text = testUrlText,
                                    customUrlPatterns = customUrlPatterns
                                )
                                testResult = if (result.isUrl) {
                                    "✅ 将被检测并提醒分享\n域名: ${result.domain ?: "未知"}"
                                } else {
                                    "❌ 不会被检测（可能被排除或格式不正确）"
                                }
                            } catch (e: Exception) {
                                testResult = "❌ 测试失败：${e.message}"
                            }
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "测试"
                        )
                    }
                }
            )

            testResult?.let { result ->
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = if (result.startsWith("✅"))
                            MaterialTheme.colorScheme.primaryContainer
                        else
                            MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Text(
                        text = result,
                        modifier = Modifier.padding(12.dp),
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (result.startsWith("✅"))
                            MaterialTheme.colorScheme.onPrimaryContainer
                        else
                            MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }

    // 添加URL模式对话框
    if (showAddDialog) {
        AlertDialog(
            onDismissRequest = { showAddDialog = false },
            title = { Text("添加URL模式") },
            text = {
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "输入URL模式（支持正则表达式）：",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    OutlinedTextField(
                        value = newPatternText,
                        onValueChange = { newPatternText = it },
                        label = { Text("URL模式") },
                        placeholder = { Text("例如: .*\\.example\\.com.*") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )
                    Text(
                        text = "示例：\n• .*\\.github\\.com.* (匹配GitHub链接)\n• https://blog\\..* (匹配博客链接)",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (newPatternText.isNotBlank()) {
                            val newList = customUrlPatterns.toMutableList()
                            newList.add(newPatternText.trim())
                            onCustomUrlPatternsChange(newList)
                            newPatternText = ""
                            showAddDialog = false
                        }
                    }
                ) {
                    Text("添加")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showAddDialog = false
                        newPatternText = ""
                    }
                ) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 配置区域组件
 */
@Composable
private fun ConfigSection(
    title: String,
    description: String,
    content: @Composable () -> Unit
) {
    Card(
        shape = androidx.compose.foundation.shape.RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            content()
        }
    }
}
