package com.weinuo.quickcommands.ui.components.skyblue

import android.content.Context
import androidx.compose.foundation.layout.*

import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.data.SmartReminderType
import com.weinuo.quickcommands.service.QuickCommandsService
import com.weinuo.quickcommands.storage.SmartReminderConfigAdapter
import com.weinuo.quickcommands.ui.configuration.ConfigurationCardItem

/**
 * 手电筒提醒配置Provider
 *
 * 提供手电筒提醒功能的配置界面和逻辑。
 * 遵循Provider架构模式，便于扩展和维护。
 *
 * 配置项包括：
 * - 延迟提醒时间（10-300秒）
 * - 提醒间隔配置（30-600秒）
 * - 自动消失设置（1-60秒）
 * - 按钮显示配置（位置、尺寸、边距）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
object SkyBlueFlashlightReminderConfigProvider {

    /**
     * 获取手电筒提醒配置项
     */
    fun getConfigurationItem(context: Context): ConfigurationCardItem<SmartReminderType> {
        return ConfigurationCardItem(
            id = "flashlight_reminder",
            title = context.getString(R.string.flashlight_reminder_title),
            description = context.getString(R.string.flashlight_reminder_description),
            operationType = SmartReminderType.FLASHLIGHT_REMINDER,
            permissionRequired = true, // 手电筒提醒需要悬浮窗权限
            content = { reminderType, onComplete ->
                FlashlightReminderConfigContent(reminderType, onComplete)
            },
            editableContent = { reminderType, initialConfig, onComplete ->
                FlashlightReminderConfigContent(reminderType, onComplete, initialConfig)
            }
        )
    }

    /**
     * 获取配置内容组件（支持外部保存请求）
     *
     * 提供手电筒提醒的配置内容组件，用于在智慧提醒详细配置界面中显示。
     * 支持外部保存请求机制，用于右上角保存按钮。
     *
     * @param reminderType 智慧提醒类型
     * @param onComplete 配置完成回调
     * @param onSaveRequested 外部保存请求回调，用于暴露保存函数给外部调用
     */
    @Composable
    fun getConfigContent(
        reminderType: SmartReminderType,
        onComplete: (Any) -> Unit,
        onSaveRequested: ((suspend () -> Unit) -> Unit)? = null
    ) {
        FlashlightReminderConfigContent(
            reminderType = reminderType,
            onComplete = onComplete,
            onSaveRequested = onSaveRequested
        )
    }
}

/**
 * 手电筒提醒配置内容组件
 *
 * 提供完整的手电筒提醒配置选项：
 * - 延迟提醒时间设置（10-300秒）
 * - 提醒间隔配置（30-600秒）
 * - 自动消失设置（1-60秒）
 * - 按钮显示配置（位置、尺寸、边距）
 *
 * @param reminderType 智慧提醒类型
 * @param onComplete 配置完成回调
 * @param initialConfig 初始配置（编辑模式使用）
 * @param onSaveRequested 外部保存请求回调，用于暴露保存函数给外部调用
 */
@Composable
private fun FlashlightReminderConfigContent(
    reminderType: SmartReminderType,
    onComplete: (Any) -> Unit,
    initialConfig: Any? = null,
    onSaveRequested: ((suspend () -> Unit) -> Unit)? = null
) {
    val context = LocalContext.current
    val configAdapter = remember { SmartReminderConfigAdapter(context) }

    // 配置状态
    var delayTime by remember { mutableStateOf("10") }
    var cooldownTime by remember { mutableStateOf("60") }
    var autoDismissEnabled by remember { mutableStateOf(false) }
    var autoDismissSeconds by remember { mutableStateOf("5") }
    var buttonPosition by remember { mutableStateOf("bottom_left") }
    var buttonSize by remember { mutableStateOf("56") }
    var buttonMarginX by remember { mutableStateOf("24") }
    var buttonMarginY by remember { mutableStateOf("24") }

    // 加载初始配置
    LaunchedEffect(initialConfig) {
        if (initialConfig is SmartReminderConfigAdapter.FlashlightReminderConfig) {
            delayTime = initialConfig.delayTime.toString()
            cooldownTime = initialConfig.cooldownTime.toString()
            autoDismissEnabled = initialConfig.autoDismissEnabled
            autoDismissSeconds = initialConfig.autoDismissSeconds.toString()
            buttonPosition = initialConfig.buttonPosition
            buttonSize = initialConfig.buttonSize.toString()
            buttonMarginX = initialConfig.buttonMarginX.toString()
            buttonMarginY = initialConfig.buttonMarginY.toString()
        }
    }

    // 保存配置函数
    val saveConfig: suspend () -> Unit = {
        try {
            val config = SmartReminderConfigAdapter.FlashlightReminderConfig(
                delayTime = delayTime.toIntOrNull()?.coerceIn(10, 300) ?: 10,
                cooldownTime = cooldownTime.toIntOrNull()?.coerceIn(30, 600) ?: 60,
                autoDismissEnabled = autoDismissEnabled,
                autoDismissSeconds = autoDismissSeconds.toIntOrNull()?.coerceIn(1, 60) ?: 5,
                buttonPosition = buttonPosition,
                buttonSize = buttonSize.toIntOrNull()?.coerceIn(40, 80) ?: 56,
                buttonMarginX = buttonMarginX.toIntOrNull()?.coerceIn(8, 100) ?: 24,
                buttonMarginY = buttonMarginY.toIntOrNull()?.coerceIn(8, 100) ?: 24
            )

            // 保存配置
            configAdapter.saveFlashlightReminderConfig(reminderType.id, config)

            // 标记为已配置
            configAdapter.updateConfiguredState(reminderType.id, true)

            // 通知服务重新加载配置
            QuickCommandsService.recheckSmartReminderMonitoring(context)

            // 注意：这里不调用onComplete，避免自动跳转
            // onComplete("flashlight_config_saved")
        } catch (e: Exception) {
            // 配置保存失败，可以在这里处理错误
        }
    }

    // 暴露保存函数给外部调用
    LaunchedEffect(onSaveRequested) {
        onSaveRequested?.invoke(saveConfig)
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 延迟提醒时间设置
        ConfigSection(
            title = "延迟提醒时间",
            description = "手电筒开启后多久开始提醒（10-300秒）"
        ) {
            OutlinedTextField(
                value = delayTime,
                onValueChange = { delayTime = it },
                label = { Text("延迟时间（秒）") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 提醒间隔设置
        ConfigSection(
            title = "提醒间隔",
            description = "两次提醒之间的最小间隔时间（30-600秒）"
        ) {
            OutlinedTextField(
                value = cooldownTime,
                onValueChange = { cooldownTime = it },
                label = { Text("间隔时间（秒）") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 自动消失设置
        ConfigSection(
            title = "自动消失",
            description = "设置提醒按钮是否自动消失，避免长时间停留在屏幕上"
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                // 自动消失开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "启用自动消失",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "提醒按钮将在指定时间后自动关闭",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    Switch(
                        checked = autoDismissEnabled,
                        onCheckedChange = { autoDismissEnabled = it }
                    )
                }

                // 自动消失时间设置
                if (autoDismissEnabled) {
                    OutlinedTextField(
                        value = autoDismissSeconds,
                        onValueChange = { autoDismissSeconds = it },
                        label = { Text("自动消失时间（秒）") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }

        // 按钮位置配置
        ConfigSection(
            title = "按钮位置",
            description = "设置提醒按钮在屏幕上的显示位置"
        ) {
            val positionOptions = listOf(
                "bottom_left" to "左下角",
                "bottom_right" to "右下角",
                "top_left" to "左上角",
                "top_right" to "右上角"
            )

            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                positionOptions.forEach { (value, label) ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = buttonPosition == value,
                            onClick = { buttonPosition = value }
                        )
                        Text(
                            text = label,
                            modifier = Modifier.padding(start = 8.dp),
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }

        // 按钮样式配置
        ConfigSection(
            title = "按钮样式",
            description = "设置提醒按钮的尺寸和边距"
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                // 按钮尺寸
                OutlinedTextField(
                    value = buttonSize,
                    onValueChange = { buttonSize = it },
                    label = { Text("按钮尺寸（dp）") },
                    placeholder = { Text("56") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )

                // 水平边距
                OutlinedTextField(
                    value = buttonMarginX,
                    onValueChange = { buttonMarginX = it },
                    label = { Text("水平边距（dp）") },
                    placeholder = { Text("24") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )

                // 垂直边距
                OutlinedTextField(
                    value = buttonMarginY,
                    onValueChange = { buttonMarginY = it },
                    label = { Text("垂直边距（dp）") },
                    placeholder = { Text("24") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

/**
 * 配置区域组件
 * 复用屏幕旋转提醒配置界面的ConfigSection组件
 */
@Composable
private fun ConfigSection(
    title: String,
    description: String,
    content: @Composable () -> Unit
) {
    Card(
        shape = androidx.compose.foundation.shape.RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            content()
        }
    }
}